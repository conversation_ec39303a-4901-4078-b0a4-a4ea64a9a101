package topic

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/kps"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"tasrv/internal/logic/activity"
	"tasrv/internal/logic/item"
	"tasrv/internal/logic/login"
	"tasrv/internal/pubsub"
)

// InitService 初始化消费者服务
func InitService() *kps.ConsumerService {
	// 初始化上报服务主题配置
	var topics []*kps.TopicRegistration

	// 登录
	topics = append(topics,
		kps.RegisterTopic(analyze.EventRegister, login.ProcessRegisterMessage),
		kps.RegisterTopic(analyze.EventLogout, login.ProcessLogoutMessage),
		kps.RegisterTopic(analyze.EventLogin, login.ProcessLoginMessage),
	)

	// 道具
	topics = append(topics,
		kps.RegisterTopic(analyze.EventItem, item.ProcessItemMessage),
	)

	// 活动
	topics = append(topics,
		kps.RegisterTopic(analyze.EventActClaimReward, activity.ProcessActivityMessage),
	)

	return pubsub.NewConsumerService(topics)
}
