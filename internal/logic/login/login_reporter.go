package login

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_login"
	"tasrv/internal/logic/common"
	"tasrv/internal/model"
)

func ProcessLoginMessage(ctx context.Context, data []byte) error {
	logrus.Printf("ProcessLoginMessage: %s", string(data))

	var teLoginInfo a_login.TeLoginInfo
	err := json.Unmarshal(data, &teLoginInfo)
	if err != nil {
		logrus.Errorf("ProcessLoginMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return err
	}

	// 转换上报信息
	record := model.TeLoginRecord{
		DefaultHeader: teLoginInfo.DefaultHeader,
		TeLogin:       teLoginInfo.TeLogin,
	}
	playerId := cast.ToString(teLoginInfo.PlayerId)
	// 记录登录时间
	err = new(dao.Tracker).Login(ctx, playerId, record.LastLoginTime)
	if err != nil {
		logrus.<PERSON><PERSON><PERSON>("ProcessLoginMessage dao login error, record:%+v, err:%s", record, err)
		return err
	}

	services.GTeService.Track(playerId, teLoginInfo.GetEvent(), &record, teLoginInfo.LastLoginTime.Unix())
	return nil
}

func ProcessRegisterMessage(ctx context.Context, data []byte) error {
	logrus.Printf("ProcessRegisterMessage: %s", string(data))

	var teRegisterInfo a_login.TeRegisterInfo
	err := json.Unmarshal(data, &teRegisterInfo)
	if err != nil {
		logrus.Errorf("ProcessRegisterMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return err
	}

	playerId := cast.ToString(teRegisterInfo.PlayerId)

	// 转换上报信息
	record := model.TeRegisterRecord{
		DefaultHeader: teRegisterInfo.DefaultHeader,
		TeRegister:    teRegisterInfo.TeRegister,
	}

	services.GTeService.UserSetOnce(playerId, &record)
	services.GTeService.Track(playerId, teRegisterInfo.GetEvent(), &record, teRegisterInfo.RegisterTime.Unix())
	return nil
}

func ProcessLogoutMessage(ctx context.Context, data []byte) error {
	logrus.Printf("ProcessLogoutMessage: %s", string(data))

	var teLogoutInfo a_login.TeLogoutInfo
	err := json.Unmarshal(data, &teLogoutInfo)
	if err != nil {
		logrus.Errorf("ProcessLogoutMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return err
	}

	playerId := cast.ToString(teLogoutInfo.PlayerId)

	onlineTime, err := new(dao.Tracker).Logout(ctx, playerId, teLogoutInfo.LogoutTime)
	if err != nil {
		logrus.Errorf("ProcessLogoutMessage dao logout error, teLogoutInfo:%+v, err:%s", teLogoutInfo, err)
		return err
	}
	// 转换上报信息
	record := model.TeLogoutRecord{
		DefaultHeader:  teLogoutInfo.DefaultHeader,
		TeLogout:       teLogoutInfo.TeLogout,
		ThisOnlineTime: onlineTime,
	}

	services.GTeService.Track(playerId, teLogoutInfo.GetEvent(), &record, teLogoutInfo.LogoutTime.Unix())
	return nil
}
