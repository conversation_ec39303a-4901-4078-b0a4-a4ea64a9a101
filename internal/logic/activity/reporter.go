package activity

import (
	"context"
	"encoding/json"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_activity"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"tasrv/internal/model"
	"tasrv/internal/services"
)

func ProcessActivityMessage(ctx context.Context, data []byte) error {
	logrus.Printf("ProcessActivityMessage: %s", string(data))

	var teActivityInfo a_activity.TeActClaimRewardInfo
	err := json.Unmarshal(data, &teActivityInfo)
	if err != nil {
		logrus.Errorf("ProcessActivityMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return err
	}

	playerId := cast.ToString(teActivityInfo.PlayerId)

	// 转换上报信息
	record := model.TeActivityRecord{
		DefaultHeader:          teActivityInfo.DefaultHeader,
		TeActClaimRewardDetail: teActivityInfo.TeActClaimRewardDetail,
	}

	services.GTeService.Track(playerId, teActivityInfo.GetEvent(), &record, teActivityInfo.OperateTime.Unix())
	return nil
}
