package activity

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_activity"
	"tasrv/internal/logic/common"
	"tasrv/internal/model"
)

func ProcessActivityMessage(ctx context.Context, data []byte) error {
	logrus.Printf("ProcessActivityMessage: %s", string(data))

	var teActivityInfo a_activity.TeActClaimRewardInfo
	err := json.Unmarshal(data, &teActivityInfo)
	if err != nil {
		logrus.Errorf("ProcessActivityMessage json.Unmarshal error, data:%+v, err:%s", data, err)
		return err
	}

	playerId := cast.ToString(teActivityInfo.PlayerId)

	// 转换上报信息
	record := model.TeActivityRecord{
		DefaultHeader:          teActivityInfo.DefaultHeader,
		TeActClaimRewardDetail: teActivityInfo.TeActClaimRewardDetail,
	}

	services.GTeService.Track(playerId, teActivityInfo.GetEvent(), &record, teActivityInfo.OperateTime.Unix())
	return nil
}
