package model

import (
	"encoding/json"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_activity"
)

type TeActivityRecord struct {
	*analyze.DefaultHeader
	*a_activity.TeActClaimRewardDetail
}

func (te *TeActivityRecord) ToMap() map[string]interface{} {
	data, _ := json.Marshal(te)
	m := make(map[string]interface{})
	json.Unmarshal(data, &m)
	return m
}
